
import { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle, Loader2 } from 'lucide-react';
import { useTeams, useDehackingTeams } from '@/hooks/useTeams';
import { TeamCard } from '@/components/teams/TeamCard';
import { AddTeamDialog } from '@/components/teams/AddTeamDialog';

const TeamManagementPage = () => {
  const [isAddTeamOpen, setIsAddTeamOpen] = useState(false);
  const { data: teams, isLoading, error } = useTeams();

  // Ensure dehacking teams are created by calling the hook
  const { data: dehackingTeams } = useDehackingTeams();

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-slate-800">Team Management</h1>
        <Button onClick={() => setIsAddTeamOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" /> Add Team
        </Button>
      </div>
      
      <div className="py-4">
        {isLoading && <div className="flex justify-center py-10"><Loader2 className="h-8 w-8 animate-spin text-slate-500" /></div>}
        {error && <div className="text-red-500 text-center py-10">Error loading teams: {error.message}</div>}

        {teams && teams.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {teams?.map(team => (
                <TeamCard key={team.id} team={team} />
            ))}
            </div>
        ) : !isLoading && !error && (
            <div className="text-center text-gray-500 py-10 border-2 border-dashed rounded-lg">
                <h2 className="text-xl font-semibold">No teams found.</h2>
                <p className="mt-2">Click "Add Team" to get started.</p>
            </div>
        )}
      </div>
      
      {isAddTeamOpen && <AddTeamDialog isOpen={isAddTeamOpen} onClose={() => setIsAddTeamOpen(false)} />}
    </>
  );
};

export default TeamManagementPage;

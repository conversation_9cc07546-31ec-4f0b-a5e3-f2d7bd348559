
import { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle, Loader2, Users } from 'lucide-react';
import { useTeams, useDehackingTeams } from '@/hooks/useTeams';
import { TeamCard } from '@/components/teams/TeamCard';
import { AddTeamDialog } from '@/components/teams/AddTeamDialog';
import { createDehackingTeams } from '@/data/teamData';
import { runCreateDehackingTeamsScript } from '@/utils/createDehackingTeamsScript';
import { toast } from 'sonner';
import { useQueryClient } from '@tanstack/react-query';

const TeamManagementPage = () => {
  const [isAddTeamOpen, setIsAddTeamOpen] = useState(false);
  const [isCreatingDehackingTeams, setIsCreatingDehackingTeams] = useState(false);
  const { data: teams, isLoading, error } = useTeams();
  const queryClient = useQueryClient();

  // Ensure dehacking teams are created by calling the hook
  const { data: dehackingTeams } = useDehackingTeams();

  // Check if dehacking teams exist
  const dehackingTeamsExist = teams?.some(team =>
    team.id === 'dehacking-team-mercy' || team.id === 'dehacking-team-taurai'
  );

  // Debug: Log current teams
  console.log('Teams loaded:', teams?.map(t => ({ id: t.id, name: t.name })));
  console.log('Dehacking teams exist:', dehackingTeamsExist);

  const handleCreateDehackingTeams = async () => {
    setIsCreatingDehackingTeams(true);
    try {
      console.log('Current teams:', teams?.map(t => ({ id: t.id, name: t.name })));

      // Use the more robust script
      await runCreateDehackingTeamsScript();

      // Refresh the teams data
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['dehackingTeams'] });

      toast.success("Dehacking teams created successfully!");
    } catch (error) {
      console.error('Error creating dehacking teams:', error);
      toast.error("Failed to create dehacking teams. Please check the console for details.");
    } finally {
      setIsCreatingDehackingTeams(false);
    }
  };

  return (
    <>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-slate-800">Team Management</h1>
        <div className="flex gap-3">
          {!dehackingTeamsExist && (
            <Button
              onClick={handleCreateDehackingTeams}
              disabled={isCreatingDehackingTeams}
              variant="outline"
              className="bg-blue-50 hover:bg-blue-100 border-blue-200"
            >
              <Users className="mr-2 h-4 w-4" />
              {isCreatingDehackingTeams ? "Creating..." : "Create Dehacking Teams"}
            </Button>
          )}
          <Button onClick={() => setIsAddTeamOpen(true)}>
            <PlusCircle className="mr-2 h-4 w-4" /> Add Team
          </Button>
        </div>
      </div>
      
      <div className="py-4">
        {isLoading && <div className="flex justify-center py-10"><Loader2 className="h-8 w-8 animate-spin text-slate-500" /></div>}
        {error && <div className="text-red-500 text-center py-10">Error loading teams: {error.message}</div>}

        {teams && teams.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {teams?.map(team => (
                <TeamCard key={team.id} team={team} />
            ))}
            </div>
        ) : !isLoading && !error && (
            <div className="text-center text-gray-500 py-10 border-2 border-dashed rounded-lg">
                <h2 className="text-xl font-semibold">No teams found.</h2>
                <p className="mt-2">Click "Add Team" to get started.</p>
            </div>
        )}
      </div>
      
      {isAddTeamOpen && <AddTeamDialog isOpen={isAddTeamOpen} onClose={() => setIsAddTeamOpen(false)} />}
    </>
  );
};

export default TeamManagementPage;

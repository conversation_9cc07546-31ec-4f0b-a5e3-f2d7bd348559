
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getTeamsWithMembers,
  getUnassignedEmployees,
  addTeam,
  deleteTeam,
  addTeamMember,
  removeTeamMember,
  getDehackingTeams,
  createDehackingTeams,
} from "@/data/teamData";
import { toast } from "sonner";

export const useTeams = () => {
  return useQuery({
    queryKey: ['teamsWithMembers'],
    queryFn: getTeamsWithMembers,
  });
};

export const useUnassignedEmployees = () => {
    return useQuery({
        queryKey: ['unassignedEmployees'],
        queryFn: getUnassignedEmployees,
    });
}

export const useAddTeam = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: addTeam,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      toast.success("Team added successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDeleteTeam = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteTeam,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['teams'] });
      toast.success("Team deleted successfully.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useAddTeamMember = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: addTeamMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['unassignedEmployees'] });
      toast.success("Member added to team.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useRemoveTeamMember = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: removeTeamMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teamsWithMembers'] });
      queryClient.invalidateQueries({ queryKey: ['unassignedEmployees'] });
      toast.success("Member removed from team.");
    },
    onError: (error: Error) => {
      toast.error(error.message);
    },
  });
};

export const useDehackingTeams = () => {
  return useQuery({
    queryKey: ['dehackingTeams'],
    queryFn: async () => {
      // First try to get existing teams
      const teams = await getDehackingTeams();

      // If no teams exist, create them
      if (teams.length === 0) {
        await createDehackingTeams();
        // Fetch again after creation
        return await getDehackingTeams();
      }

      return teams;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

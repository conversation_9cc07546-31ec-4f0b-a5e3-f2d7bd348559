
import { supabase } from "@/integrations/supabase/client";
import { Employee } from './employeeData';
import { Team } from './fuelBunkersData';
import { v4 as uuidv4 } from "uuid";

export interface TeamWithMembers extends Team {
  members: (Employee & { membershipId: string })[];
}

export const getTeamsWithMembers = async (): Promise<TeamWithMembers[]> => {
  const { data: teams, error: teamsError } = await supabase.from('teams').select('*');
  if (teamsError) throw teamsError;
  if (!teams) return [];

  const { data: memberships, error: membershipsError } = await supabase
    .from('team_memberships')
    .select('id, team_id, employees(*)');
  if (membershipsError) throw membershipsError;

  const teamsWithMembers = teams.map(team => {
    const members = memberships
      ?.filter((m: any) => m.team_id === team.id && m.employees)
      .map((m: any) => ({
        ...(m.employees as Employee),
        membershipId: m.id,
      })) || [];
    return { ...team, members };
  });

  return teamsWithMembers;
};

export const getUnassignedEmployees = async (): Promise<Employee[]> => {
  const { data: allEmployees, error: employeesError } = await supabase.from('employees').select('*');
  if (employeesError) throw employeesError;
  if (!allEmployees) return [];

  const { data: memberships, error: membershipsError } = await supabase.from('team_memberships').select('employee_id');
  if (membershipsError) throw membershipsError;
  if (!memberships) return allEmployees;

  const assignedEmployeeIds = new Set(memberships.map((m: any) => m.employee_id));
  
  return allEmployees.filter(e => !assignedEmployeeIds.has(e.id));
};

export const addTeam = async (name: string) => {
  const { data, error } = await supabase.from('teams').insert([{ id: uuidv4(), name }]).select().single();
  if (error) throw error;
  return data;
};

export const deleteTeam = async (id: string) => {
  const { error } = await supabase.from('teams').delete().eq('id', id);
  if (error) throw error;
};

export const addTeamMember = async ({ teamId, employeeId }: { teamId: string, employeeId: number }) => {
  const { data, error } = await supabase.from('team_memberships').insert([{ team_id: teamId, employee_id: employeeId }]).select().single();
  if (error) throw error;
  return data;
};

export const removeTeamMember = async (membershipId: string) => {
  const { error } = await supabase.from('team_memberships').delete().eq('id', membershipId);
  if (error) throw error;
};

export const getDehackingTeams = async (): Promise<Team[]> => {
  const { data, error } = await supabase
    .from('teams')
    .select('*')
    .in('id', ['dehacking-team-mercy', 'dehacking-team-taurai']);
  if (error) throw error;
  return data || [];
};

export const createDehackingTeams = async () => {
  const teams = [
    { id: 'dehacking-team-mercy', name: 'Dehacking Team Mercy' },
    { id: 'dehacking-team-taurai', name: 'Dehacking Team Taurai' }
  ];

  console.log('Creating dehacking teams...');

  for (const team of teams) {
    try {
      // First check if team already exists
      const { data: existingTeam } = await supabase
        .from('teams')
        .select('id')
        .eq('id', team.id)
        .single();

      if (existingTeam) {
        console.log(`Team ${team.name} already exists`);
        continue;
      }

      // Create the team
      const { data, error } = await supabase
        .from('teams')
        .insert(team)
        .select()
        .single();

      if (error) {
        console.error(`Error creating team ${team.name}:`, error);
        throw error;
      } else {
        console.log(`Successfully created team: ${team.name}`, data);
      }
    } catch (error) {
      console.error(`Failed to create team ${team.name}:`, error);
      throw error;
    }
  }

  console.log('Dehacking teams creation completed');
};

export const isDehackingTeam = (teamId: string): boolean => {
  return teamId === 'dehacking-team-mercy' || teamId === 'dehacking-team-taurai';
};

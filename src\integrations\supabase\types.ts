export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      assets: {
        Row: {
          id: string
          name: string
          status: string
          type: string
        }
        Insert: {
          id: string
          name: string
          status?: string
          type: string
        }
        Update: {
          id?: string
          name?: string
          status?: string
          type?: string
        }
        Relationships: []
      }
      dehacking_entries: {
        Row: {
          brick_type_id: string
          created_at: string
          date: string
          employee_id: number
          hour: number
          id: number
          pallet_count: number
          team_id: string | null
        }
        Insert: {
          brick_type_id: string
          created_at?: string
          date: string
          employee_id: number
          hour: number
          id?: number
          pallet_count: number
          team_id?: string | null
        }
        Update: {
          brick_type_id?: string
          created_at?: string
          date?: string
          employee_id?: number
          hour?: number
          id?: number
          pallet_count?: number
          team_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "dehacking_entries_brick_type_id_fkey"
            columns: ["brick_type_id"]
            isOneToOne: false
            referencedRelation: "management_brick_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "dehacking_entries_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      employee_roles: {
        Row: {
          employee_id: number
          id: number
          role: string
        }
        Insert: {
          employee_id: number
          id?: number
          role: string
        }
        Update: {
          employee_id?: number
          id?: number
          role?: string
        }
        Relationships: [
          {
            foreignKeyName: "employee_roles_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      employees: {
        Row: {
          department: string | null
          employee_code: string | null
          id: number
          name: string
          performance: number
          role: string | null
          status: string
        }
        Insert: {
          department?: string | null
          employee_code?: string | null
          id?: number
          name: string
          performance?: number
          role?: string | null
          status?: string
        }
        Update: {
          department?: string | null
          employee_code?: string | null
          id?: number
          name?: string
          performance?: number
          role?: string | null
          status?: string
        }
        Relationships: []
      }
      fires: {
        Row: {
          id: string
          kiln_id: string
          name: string
          status: Database["public"]["Enums"]["fire_status"]
        }
        Insert: {
          id: string
          kiln_id: string
          name: string
          status: Database["public"]["Enums"]["fire_status"]
        }
        Update: {
          id?: string
          kiln_id?: string
          name?: string
          status?: Database["public"]["Enums"]["fire_status"]
        }
        Relationships: [
          {
            foreignKeyName: "fires_kiln_id_fkey"
            columns: ["kiln_id"]
            isOneToOne: false
            referencedRelation: "kilns"
            referencedColumns: ["id"]
          },
        ]
      }
      forklift_allocations: {
        Row: {
          allocated_teams: string[]
          allocation_type: string
          end_date: string | null
          forklift_driver_id: number
          id: number
          is_active: boolean
          start_date: string
        }
        Insert: {
          allocated_teams: string[]
          allocation_type: string
          end_date?: string | null
          forklift_driver_id: number
          id?: number
          is_active?: boolean
          start_date?: string
        }
        Update: {
          allocated_teams?: string[]
          allocation_type?: string
          end_date?: string | null
          forklift_driver_id?: number
          id?: number
          is_active?: boolean
          start_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "forklift_allocations_forklift_driver_id_fkey"
            columns: ["forklift_driver_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      fuel_bunkers: {
        Row: {
          capacity: number
          current_level: number
          id: string
          name: string
        }
        Insert: {
          capacity: number
          current_level: number
          id: string
          name: string
        }
        Update: {
          capacity?: number
          current_level?: number
          id?: string
          name?: string
        }
        Relationships: []
      }
      fuel_deliveries: {
        Row: {
          cost_per_liter: number
          created_at: string
          delivery_date: string
          fuel_bunker_id: string
          id: string
          invoice_number: string
          quantity: number
          supplier: string
        }
        Insert: {
          cost_per_liter: number
          created_at?: string
          delivery_date: string
          fuel_bunker_id: string
          id?: string
          invoice_number: string
          quantity: number
          supplier: string
        }
        Update: {
          cost_per_liter?: number
          created_at?: string
          delivery_date?: string
          fuel_bunker_id?: string
          id?: string
          invoice_number?: string
          quantity?: number
          supplier?: string
        }
        Relationships: [
          {
            foreignKeyName: "fuel_deliveries_fuel_bunker_id_fkey"
            columns: ["fuel_bunker_id"]
            isOneToOne: false
            referencedRelation: "fuel_bunkers"
            referencedColumns: ["id"]
          },
        ]
      }
      fuel_dispensing_transactions: {
        Row: {
          asset_id: string
          created_at: string
          ending_reading: number | null
          fuel_bunker_id: string
          id: string
          notes: string | null
          operator_id: number
          quantity_liters: number
          starting_reading: number | null
          transaction_date: string
        }
        Insert: {
          asset_id: string
          created_at?: string
          ending_reading?: number | null
          fuel_bunker_id: string
          id?: string
          notes?: string | null
          operator_id: number
          quantity_liters: number
          starting_reading?: number | null
          transaction_date?: string
        }
        Update: {
          asset_id?: string
          created_at?: string
          ending_reading?: number | null
          fuel_bunker_id?: string
          id?: string
          notes?: string | null
          operator_id?: number
          quantity_liters?: number
          starting_reading?: number | null
          transaction_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "fuel_dispensing_transactions_asset_id_fkey"
            columns: ["asset_id"]
            isOneToOne: false
            referencedRelation: "assets"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fuel_dispensing_transactions_fuel_bunker_id_fkey"
            columns: ["fuel_bunker_id"]
            isOneToOne: false
            referencedRelation: "fuel_bunkers"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fuel_dispensing_transactions_operator_id_fkey"
            columns: ["operator_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      kilns: {
        Row: {
          id: string
          name: string
          status: Database["public"]["Enums"]["kiln_status"]
        }
        Insert: {
          id: string
          name: string
          status: Database["public"]["Enums"]["kiln_status"]
        }
        Update: {
          id?: string
          name?: string
          status?: Database["public"]["Enums"]["kiln_status"]
        }
        Relationships: []
      }
      management_brick_types: {
        Row: {
          brick_stage: string
          bricks_per_pallet: number
          category: string
          dehacking_day_rate: number | null
          dehacking_night_rate: number | null
          dehacking_rate: number
          grade: string
          id: string
          name: string
          overtime_rate: number
          setting_day_rate: number | null
          setting_night_rate: number | null
          setting_rate: number
          status: string
        }
        Insert: {
          brick_stage?: string
          bricks_per_pallet: number
          category: string
          dehacking_day_rate?: number | null
          dehacking_night_rate?: number | null
          dehacking_rate: number
          grade: string
          id: string
          name: string
          overtime_rate: number
          setting_day_rate?: number | null
          setting_night_rate?: number | null
          setting_rate: number
          status: string
        }
        Update: {
          brick_stage?: string
          bricks_per_pallet?: number
          category?: string
          dehacking_day_rate?: number | null
          dehacking_night_rate?: number | null
          dehacking_rate?: number
          grade?: string
          id?: string
          name?: string
          overtime_rate?: number
          setting_day_rate?: number | null
          setting_night_rate?: number | null
          setting_rate?: number
          status?: string
        }
        Relationships: []
      }
      notification_settings: {
        Row: {
          created_at: string | null
          email_notifications: boolean | null
          employee_updates: boolean | null
          fuel_level_warnings: boolean | null
          id: string
          production_alerts: boolean | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string | null
          email_notifications?: boolean | null
          employee_updates?: boolean | null
          fuel_level_warnings?: boolean | null
          id?: string
          production_alerts?: boolean | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string | null
          email_notifications?: boolean | null
          employee_updates?: boolean | null
          fuel_level_warnings?: boolean | null
          id?: string
          production_alerts?: boolean | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notification_settings_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      pallet_movements: {
        Row: {
          comments: string | null
          created_at: string | null
          delivery_date: string
          delivery_note: string
          destination: string
          id: string
          pallets_loaded: number
          product_type: string
          status: string
          vehicle_registration: string
        }
        Insert: {
          comments?: string | null
          created_at?: string | null
          delivery_date: string
          delivery_note: string
          destination: string
          id?: string
          pallets_loaded: number
          product_type: string
          status?: string
          vehicle_registration: string
        }
        Update: {
          comments?: string | null
          created_at?: string | null
          delivery_date?: string
          delivery_note?: string
          destination?: string
          id?: string
          pallets_loaded?: number
          product_type?: string
          status?: string
          vehicle_registration?: string
        }
        Relationships: []
      }
      pallet_returns: {
        Row: {
          comments: string | null
          condition: string
          created_at: string | null
          id: string
          pallet_movement_id: string
          pallets_returned: number
          return_date: string
        }
        Insert: {
          comments?: string | null
          condition: string
          created_at?: string | null
          id?: string
          pallet_movement_id: string
          pallets_returned: number
          return_date: string
        }
        Update: {
          comments?: string | null
          condition?: string
          created_at?: string | null
          id?: string
          pallet_movement_id?: string
          pallets_returned?: number
          return_date?: string
        }
        Relationships: [
          {
            foreignKeyName: "pallet_returns_pallet_movement_id_fkey"
            columns: ["pallet_movement_id"]
            isOneToOne: false
            referencedRelation: "pallet_movements"
            referencedColumns: ["id"]
          },
        ]
      }
      payments: {
        Row: {
          amount: number
          created_at: string
          employee_id: number
          id: string
          notes: string | null
          payment_date: string
          payment_type: Database["public"]["Enums"]["payment_type"]
          status: Database["public"]["Enums"]["payment_status"]
        }
        Insert: {
          amount: number
          created_at?: string
          employee_id: number
          id?: string
          notes?: string | null
          payment_date: string
          payment_type: Database["public"]["Enums"]["payment_type"]
          status?: Database["public"]["Enums"]["payment_status"]
        }
        Update: {
          amount?: number
          created_at?: string
          employee_id?: number
          id?: string
          notes?: string | null
          payment_date?: string
          payment_type?: Database["public"]["Enums"]["payment_type"]
          status?: Database["public"]["Enums"]["payment_status"]
        }
        Relationships: [
          {
            foreignKeyName: "payments_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
        ]
      }
      production_entries: {
        Row: {
          brick_type_id: string
          created_at: string
          date: string
          id: number
          pallet_count: number
        }
        Insert: {
          brick_type_id: string
          created_at?: string
          date: string
          id?: number
          pallet_count: number
        }
        Update: {
          brick_type_id?: string
          created_at?: string
          date?: string
          id?: number
          pallet_count?: number
        }
        Relationships: [
          {
            foreignKeyName: "production_entries_brick_type_id_fkey"
            columns: ["brick_type_id"]
            isOneToOne: false
            referencedRelation: "management_brick_types"
            referencedColumns: ["id"]
          },
        ]
      }
      setting_production_entries: {
        Row: {
          brick_type_id: string
          created_at: string
          date: string
          fire_id: string
          id: number
          pallet_count: number
          team_id: string
        }
        Insert: {
          brick_type_id: string
          created_at?: string
          date?: string
          fire_id: string
          id?: number
          pallet_count: number
          team_id: string
        }
        Update: {
          brick_type_id?: string
          created_at?: string
          date?: string
          fire_id?: string
          id?: number
          pallet_count?: number
          team_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "setting_production_entries_brick_type_id_fkey"
            columns: ["brick_type_id"]
            isOneToOne: false
            referencedRelation: "management_brick_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "setting_production_entries_fire_id_fkey"
            columns: ["fire_id"]
            isOneToOne: false
            referencedRelation: "fires"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "setting_production_entries_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      system_config: {
        Row: {
          auto_backup: boolean | null
          company_name: string | null
          created_at: string | null
          currency: string | null
          dashboard_layout: string | null
          id: string
          language: string | null
          theme: string | null
          timezone: string | null
          updated_at: string | null
        }
        Insert: {
          auto_backup?: boolean | null
          company_name?: string | null
          created_at?: string | null
          currency?: string | null
          dashboard_layout?: string | null
          id?: string
          language?: string | null
          theme?: string | null
          timezone?: string | null
          updated_at?: string | null
        }
        Update: {
          auto_backup?: boolean | null
          company_name?: string | null
          created_at?: string | null
          currency?: string | null
          dashboard_layout?: string | null
          id?: string
          language?: string | null
          theme?: string | null
          timezone?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      team_memberships: {
        Row: {
          created_at: string
          employee_id: number
          id: string
          team_id: string
        }
        Insert: {
          created_at?: string
          employee_id: number
          id?: string
          team_id: string
        }
        Update: {
          created_at?: string
          employee_id?: number
          id?: string
          team_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "team_memberships_employee_id_fkey"
            columns: ["employee_id"]
            isOneToOne: false
            referencedRelation: "employees"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_memberships_team_id_fkey"
            columns: ["team_id"]
            isOneToOne: false
            referencedRelation: "teams"
            referencedColumns: ["id"]
          },
        ]
      }
      teams: {
        Row: {
          id: string
          name: string
        }
        Insert: {
          id: string
          name: string
        }
        Update: {
          id?: string
          name?: string
        }
        Relationships: []
      }
      users: {
        Row: {
          active: boolean | null
          created_at: string | null
          email: string | null
          full_name: string
          id: string
          password_hash: string
          role: Database["public"]["Enums"]["user_role"] | null
          updated_at: string | null
          username: string
        }
        Insert: {
          active?: boolean | null
          created_at?: string | null
          email?: string | null
          full_name: string
          id?: string
          password_hash: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
          username: string
        }
        Update: {
          active?: boolean | null
          created_at?: string | null
          email?: string | null
          full_name?: string
          id?: string
          password_hash?: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string | null
          username?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_current_user_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_current_user_role: {
        Args: Record<PropertyKey, never>
        Returns: Database["public"]["Enums"]["user_role"]
      }
      is_current_user_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      set_config: {
        Args: {
          setting_name: string
          setting_value: string
          is_local?: boolean
        }
        Returns: undefined
      }
    }
    Enums: {
      fire_status: "active" | "inactive" | "maintenance"
      kiln_status: "operational" | "offline" | "maintenance"
      payment_status: "Paid" | "Pending" | "Processing" | "Failed"
      payment_type:
        | "Salary"
        | "Bonus"
        | "Overtime"
        | "Dehacking"
        | "Setting"
        | "Adjustment"
      user_role:
        | "admin"
        | "manager"
        | "finance"
        | "factory_supervisor"
        | "yard_supervisor"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      fire_status: ["active", "inactive", "maintenance"],
      kiln_status: ["operational", "offline", "maintenance"],
      payment_status: ["Paid", "Pending", "Processing", "Failed"],
      payment_type: [
        "Salary",
        "Bonus",
        "Overtime",
        "Dehacking",
        "Setting",
        "Adjustment",
      ],
      user_role: [
        "admin",
        "manager",
        "finance",
        "factory_supervisor",
        "yard_supervisor",
      ],
    },
  },
} as const

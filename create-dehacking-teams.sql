-- <PERSON><PERSON>t to create the dehacking teams and add team_id column to dehacking_entries
-- Run this in your Supabase SQL editor

-- First, add the team_id column to dehacking_entries if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'dehacking_entries' 
                   AND column_name = 'team_id') THEN
        ALTER TABLE public.dehacking_entries 
        ADD COLUMN team_id TEXT REFERENCES public.teams(id);
    END IF;
END $$;

-- Create the two specific dehacking teams
INSERT INTO public.teams (id, name) VALUES 
('dehacking-team-mercy', 'Dehacking Team Mercy'),
('dehacking-team-taurai', 'Dehacking Team Taurai')
ON CONFLICT (id) DO NOTHING;

-- Verify the teams were created
SELECT * FROM public.teams WHERE id IN ('dehacking-team-mercy', 'dehacking-team-taurai');

// Utility script to create dehacking teams
// This can be run directly in the browser console or called from the app

import { supabase } from "@/integrations/supabase/client";

export const runCreateDehackingTeamsScript = async () => {
  console.log('🚀 Starting dehacking teams creation script...');
  
  const teams = [
    { id: 'dehacking-team-mercy', name: 'Dehacking Team Mercy' },
    { id: 'dehacking-team-taurai', name: 'Dehacking Team Taurai' }
  ];
  
  try {
    // First, let's see what teams currently exist
    const { data: existingTeams, error: fetchError } = await supabase
      .from('teams')
      .select('*');
    
    if (fetchError) {
      console.error('❌ Error fetching existing teams:', fetchError);
      return;
    }
    
    console.log('📋 Current teams in database:', existingTeams);
    
    // Create each dehacking team
    for (const team of teams) {
      console.log(`🔄 Processing team: ${team.name}`);
      
      // Check if team already exists
      const existingTeam = existingTeams?.find(t => t.id === team.id);
      if (existingTeam) {
        console.log(`✅ Team "${team.name}" already exists`);
        continue;
      }
      
      // Create the team
      const { data, error } = await supabase
        .from('teams')
        .insert(team)
        .select()
        .single();
      
      if (error) {
        console.error(`❌ Error creating team "${team.name}":`, error);
        throw error;
      } else {
        console.log(`✅ Successfully created team "${team.name}":`, data);
      }
    }
    
    // Verify the teams were created
    const { data: finalTeams, error: finalFetchError } = await supabase
      .from('teams')
      .select('*')
      .in('id', ['dehacking-team-mercy', 'dehacking-team-taurai']);
    
    if (finalFetchError) {
      console.error('❌ Error verifying teams:', finalFetchError);
      return;
    }
    
    console.log('🎉 Dehacking teams creation completed!');
    console.log('📋 Created teams:', finalTeams);
    
    return finalTeams;
    
  } catch (error) {
    console.error('💥 Script failed:', error);
    throw error;
  }
};

// Function to be called from browser console
(window as any).createDehackingTeams = runCreateDehackingTeamsScript;

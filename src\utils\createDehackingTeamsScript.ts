// Utility script to create dehacking teams
// This can be run directly in the browser console or called from the app

import { supabase } from "@/integrations/supabase/client";

export const runCreateDehackingTeamsScript = async () => {
  console.log('🚀 Starting dehacking teams creation script...');

  const teams = [
    { id: 'dehacking-team-mercy', name: 'Dehacking Team Mercy' },
    { id: 'dehacking-team-taurai', name: 'Dehacking Team Taurai' }
  ];

  try {
    // First, let's see what teams currently exist
    console.log('🔍 Fetching existing teams...');
    const { data: existingTeams, error: fetchError } = await supabase
      .from('teams')
      .select('*');

    if (fetchError) {
      console.error('❌ Error fetching existing teams:', fetchError);
      console.error('❌ Full error details:', JSON.stringify(fetchError, null, 2));
      throw new Error(`Failed to fetch existing teams: ${fetchError.message}`);
    }

    console.log('📋 Current teams in database:', existingTeams);
    console.log('📊 Total teams found:', existingTeams?.length || 0);
    
    // Create each dehacking team
    for (const team of teams) {
      console.log(`🔄 Processing team: ${team.name} (ID: ${team.id})`);

      // Check if team already exists
      const existingTeam = existingTeams?.find(t => t.id === team.id);
      if (existingTeam) {
        console.log(`✅ Team "${team.name}" already exists with data:`, existingTeam);
        continue;
      }

      console.log(`🆕 Creating new team: ${team.name}`);

      // Create the team with specific ID and name
      console.log(`🆕 Inserting team with ID: ${team.id} and name: ${team.name}`);
      const { data, error } = await supabase
        .from('teams')
        .insert({
          id: team.id,
          name: team.name
        })
        .select()
        .single();

      if (error) {
        console.error(`❌ Error creating team "${team.name}":`, error);
        console.error(`❌ Full error details:`, JSON.stringify(error, null, 2));
        console.error(`❌ Team data being inserted:`, team);
        throw new Error(`Failed to create team "${team.name}": ${error.message}`);
      } else {
        console.log(`✅ Successfully created team "${team.name}":`, data);
      }
    }
    
    // Verify the teams were created
    console.log('🔍 Verifying teams were created...');
    const { data: finalTeams, error: finalFetchError } = await supabase
      .from('teams')
      .select('*')
      .in('id', ['dehacking-team-mercy', 'dehacking-team-taurai']);

    if (finalFetchError) {
      console.error('❌ Error verifying teams:', finalFetchError);
      console.error('❌ Verification error details:', JSON.stringify(finalFetchError, null, 2));
      throw new Error(`Failed to verify teams: ${finalFetchError.message}`);
    }

    console.log('🎉 Dehacking teams creation completed!');
    console.log('📋 Final teams found:', finalTeams);
    console.log('📊 Teams created/verified:', finalTeams?.length || 0);

    return finalTeams;

  } catch (error) {
    console.error('💥 Script failed with error:', error);
    console.error('💥 Error type:', typeof error);
    console.error('💥 Error message:', error instanceof Error ? error.message : 'Unknown error');
    console.error('💥 Full error object:', error);
    throw error;
  }
};

// Function to check what teams exist in database
export const checkTeamsInDatabase = async () => {
  console.log('🔍 Checking all teams in database...');

  try {
    const { data: allTeams, error } = await supabase.from('teams').select('*');

    if (error) {
      console.error('❌ Error fetching teams:', error);
      return;
    }

    console.log('📋 All teams in database:', allTeams);
    console.log('📊 Total teams:', allTeams?.length || 0);

    // Look for dehacking teams specifically
    const dehackingTeams = allTeams?.filter(team =>
      team.name.toLowerCase().includes('dehacking') ||
      team.name.toLowerCase().includes('mercy') ||
      team.name.toLowerCase().includes('taurai')
    );

    console.log('🎯 Dehacking-related teams:', dehackingTeams);

    // Check for exact matches
    const exactMatches = allTeams?.filter(team =>
      team.name === 'Dehacking Team Mercy' ||
      team.name === 'Dehacking Team Taurai'
    );

    console.log('✅ Exact name matches:', exactMatches);

    return { allTeams, dehackingTeams, exactMatches };
  } catch (error) {
    console.error('💥 Error checking teams:', error);
  }
};

// Functions to be called from browser console
(window as any).createDehackingTeams = runCreateDehackingTeamsScript;
(window as any).checkTeams = checkTeamsInDatabase;
